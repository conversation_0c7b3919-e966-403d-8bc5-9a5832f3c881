time=2025-07-23T14:04:47.722Z level=DEBUG msg="request headers processing" request_id=a2d22a3d-d4a0-4eb3-9346-5908c38fefc1 is_upstream_filter=true request_headers.:authority=localhost:8000 request_headers.:method=POST request_headers.:scheme=http request_headers.:path=/v1/chat/completions request_headers.x-envoy-external-address=127.0.0.1 request_headers.x-gateway-destination-endpoint=***********:8000 request_headers.x-request-id=a2d22a3d-d4a0-4eb3-9346-5908c38fefc1 request_headers.x-ai-eg-original-path=/v1/chat/completions request_headers.user-agent=curl/8.7.1 request_headers.x-forwarded-for=*********** request_headers.x-forwarded-proto=http request_headers.x-ai-eg-model=meta-llama/Llama-3.1-8B-Instruct request_headers.accept=*/* request_headers.content-type=application/json
time=2025-07-23T14:04:47.722Z level=DEBUG msg="request headers processed" request_id=a2d22a3d-d4a0-4eb3-9346-5908c38fefc1 is_upstream_filter=true response="request_headers:{response:{status:CONTINUE_AND_REPLACE  header_mutation:{set_headers:{header:{key:\":path\"  raw_value:\"/v1/chat/completions\"}}}}}  dynamic_metadata:{fields:{key:\"io.envoy.ai_gateway\"  value:{struct_value:{fields:{key:\"content_length\"  value:{number_value:158}}}}}}"
