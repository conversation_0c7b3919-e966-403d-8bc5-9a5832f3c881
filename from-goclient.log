time=2025-07-23T14:07:27.451Z level=DEBUG msg="request headers processing" request_id=0923d1da-fd07-4e9a-a25f-07a1beba5ad5 is_upstream_filter=true request_headers.:authority=127.0.0.1:53489 request_headers.:method=POST request_headers.:scheme=http request_headers.:path=/v1/chat/completions request_headers.x-envoy-external-address=127.0.0.1 request_headers.x-gateway-destination-endpoint=***********:8000 request_headers.content-type=application/json request_headers.user-agent=Go-http-client/1.1 request_headers.accept-encoding=gzip request_headers.x-forwarded-for=*********** request_headers.x-forwarded-proto=http request_headers.x-request-id=0923d1da-fd07-4e9a-a25f-07a1beba5ad5 request_headers.x-ai-eg-model=meta-llama/Llama-3.1-8B-Instruct request_headers.x-ai-eg-original-path=/v1/chat/completions
time=2025-07-23T14:07:27.451Z level=DEBUG msg="request headers processed" request_id=0923d1da-fd07-4e9a-a25f-07a1beba5ad5 is_upstream_filter=true response="request_headers:{response:{status:CONTINUE_AND_REPLACE  header_mutation:{set_headers:{header:{key:\":path\"  raw_value:\"/v1/chat/completions\"}}}}}  dynamic_metadata:{fields:{key:\"io.envoy.ai_gateway\"  value:{struct_value:{fields:{key:\"content_length\"  value:{number_value:104}}}}}}"